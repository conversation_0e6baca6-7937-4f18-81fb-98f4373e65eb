<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dots and Boxes Game</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col md:flex-row items-center justify-center p-4">
    <div class="container mx-auto max-w-6xl flex flex-col md:flex-row items-start gap-8">
        <!-- Game Board -->
        <div class="flex-1 bg-white rounded-lg shadow-lg p-6 mb-6 md:mb-0">
            <h1 class="text-3xl font-bold text-center mb-6">Dots and Boxes</h1>
            <div id="game-board" class="relative mx-auto" style="width: fit-content;"></div>
        </div>

        <!-- Sidebar -->
        <div class="w-full md:w-64 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">Game Info</h2>
            
            <!-- Current Turn -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Current Turn</h3>
                <div class="flex items-center space-x-2 mb-2">
                    <div id="player1-turn" class="flex items-center">
                        <span class="player-arrow text-red-500 text-xl">→</span>
                        <span class="font-medium">Player 1</span>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div id="player2-turn" class="flex items-center">
                        <span class="player-arrow text-blue-500 text-xl opacity-0">→</span>
                        <span class="font-medium">Player 2</span>
                    </div>
                </div>
            </div>
            
            <!-- Scoreboard -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Score</h3>
                <div class="flex justify-between items-center mb-2">
                    <span class="font-medium">Player 1:</span>
                    <span id="player1-score" class="text-red-500 font-bold">0</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="font-medium">Player 2:</span>
                    <span id="player2-score" class="text-blue-500 font-bold">0</span>
                </div>
            </div>
            
            <!-- Turn Count -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Turn Count</h3>
                <div class="text-center bg-gray-100 py-2 px-4 rounded-md">
                    <span id="turn-count" class="text-2xl font-bold">1</span>
                </div>
            </div>

            <!-- Reset Button -->
            <button id="reset-button" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                New Game
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Game Configuration
            const config = {
                gridSize: 5, // 5x5 grid of dots
                dotSize: 12,
                lineThickness: 6,
                boxSize: 60,
                colors: {
                    dot: 'black',
                    line: 'black',
                    previewLine: 'rgba(0, 0, 0, 0.3)',
                    player1Box: 'rgba(239, 68, 68, 0.7)', // red-500 with opacity
                    player2Box: 'rgba(59, 130, 246, 0.7)' // blue-500 with opacity
                }
            };

            // Game State
            const gameState = {
                currentPlayer: 1,
                scores: { 1: 0, 2: 0 },
                turnCount: 1,
                horizontalLines: Array(config.gridSize).fill().map(() => Array(config.gridSize - 1).fill(false)),
                verticalLines: Array(config.gridSize - 1).fill().map(() => Array(config.gridSize).fill(false)),
                boxes: Array(config.gridSize - 1).fill().map(() => Array(config.gridSize - 1).fill(0)),
                dragStart: null,
                previewLine: null
            };

            // DOM Elements
            const gameBoard = document.getElementById('game-board');
            const player1Turn = document.getElementById('player1-turn');
            const player2Turn = document.getElementById('player2-turn');
            const player1Score = document.getElementById('player1-score');
            const player2Score = document.getElementById('player2-score');
            const turnCount = document.getElementById('turn-count');
            const resetButton = document.getElementById('reset-button');

            // Calculate board dimensions
            const boardWidth = config.gridSize * config.boxSize;
            const boardHeight = config.gridSize * config.boxSize;

            // Initialize the game board
            function initializeBoard() {
                // Set board dimensions
                gameBoard.style.width = `${boardWidth}px`;
                gameBoard.style.height = `${boardHeight}px`;
                gameBoard.innerHTML = '';

                // Create SVG container for the game board
                const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.setAttribute('width', boardWidth);
                svg.setAttribute('height', boardHeight);
                svg.classList.add('absolute', 'top-0', 'left-0');
                gameBoard.appendChild(svg);

                // Create boxes layer (will be filled when boxes are completed)
                const boxesLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                boxesLayer.id = 'boxes-layer';
                svg.appendChild(boxesLayer);

                // Create lines layer
                const linesLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                linesLayer.id = 'lines-layer';
                svg.appendChild(linesLayer);

                // Create preview line layer
                const previewLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                previewLayer.id = 'preview-layer';
                svg.appendChild(previewLayer);

                // Create dots layer (on top)
                const dotsLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                dotsLayer.id = 'dots-layer';
                svg.appendChild(dotsLayer);

                // Draw dots
                for (let row = 0; row < config.gridSize; row++) {
                    for (let col = 0; col < config.gridSize; col++) {
                        const x = col * config.boxSize;
                        const y = row * config.boxSize;
                        
                        const dot = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                        dot.setAttribute('cx', x);
                        dot.setAttribute('cy', y);
                        dot.setAttribute('r', config.dotSize / 2);
                        dot.setAttribute('fill', config.colors.dot);
                        dot.setAttribute('data-row', row);
                        dot.setAttribute('data-col', col);
                        dot.classList.add('dot');
                        dotsLayer.appendChild(dot);

                        // Add event listeners for drag interaction
                        dot.addEventListener('mousedown', startDrag);
                        dot.addEventListener('touchstart', handleTouchStart);
                    }
                }

                // Add event listeners for drag movement and end
                svg.addEventListener('mousemove', updateDrag);
                svg.addEventListener('mouseup', endDrag);
                svg.addEventListener('mouseleave', cancelDrag);
                svg.addEventListener('touchmove', handleTouchMove);
                svg.addEventListener('touchend', handleTouchEnd);
                svg.addEventListener('touchcancel', cancelDrag);
            }

            // Touch event handlers
            function handleTouchStart(e) {
                e.preventDefault();
                const touch = e.touches[0];
                const dot = e.target;
                
                // Create a simulated mouse event
                const mouseEvent = new MouseEvent('mousedown', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                
                dot.dispatchEvent(mouseEvent);
            }

            function handleTouchMove(e) {
                if (!gameState.dragStart) return;
                
                e.preventDefault();
                const touch = e.touches[0];
                
                // Create a simulated mouse event
                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                
                document.getElementById('game-board').dispatchEvent(mouseEvent);
            }

            function handleTouchEnd(e) {
                if (!gameState.dragStart) return;
                
                e.preventDefault();
                
                // Find the closest dot to the touch end position
                const touch = e.changedTouches[0];
                const svg = document.querySelector('#game-board svg');
                const svgRect = svg.getBoundingClientRect();
                const x = touch.clientX - svgRect.left;
                const y = touch.clientY - svgRect.top;
                
                // Find the closest dot
                let closestDot = null;
                let minDistance = Infinity;
                
                document.querySelectorAll('.dot').forEach(dot => {
                    const dotX = parseFloat(dot.getAttribute('cx'));
                    const dotY = parseFloat(dot.getAttribute('cy'));
                    const distance = Math.sqrt(Math.pow(dotX - x, 2) + Math.pow(dotY - y, 2));
                    
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestDot = dot;
                    }
                });
                
                // If we found a close dot and it's within a reasonable distance
                if (closestDot && minDistance < config.boxSize / 2) {
                    // Create a simulated mouse event on that dot
                    const mouseEvent = new MouseEvent('mouseup', {
                        clientX: touch.clientX,
                        clientY: touch.clientY
                    });
                    
                    closestDot.dispatchEvent(mouseEvent);
                } else {
                    // Cancel the drag if no dot is close enough
                    cancelDrag();
                }
            }

            // Start drag operation
            function startDrag(e) {
                const dot = e.target;
                const row = parseInt(dot.getAttribute('data-row'));
                const col = parseInt(dot.getAttribute('data-col'));
                
                gameState.dragStart = { row, col };
                
                // Create preview line
                createPreviewLine(row, col, e.clientX, e.clientY);
            }

            // Update drag preview line
            function updateDrag(e) {
                if (!gameState.dragStart) return;
                
                const svg = e.currentTarget;
                const svgRect = svg.getBoundingClientRect();
                const x = e.clientX - svgRect.left;
                const y = e.clientY - svgRect.top;
                
                updatePreviewLine(x, y);
            }

            // End drag operation
            function endDrag(e) {
                if (!gameState.dragStart) return;
                
                // Check if we're over a valid dot
                if (e.target.classList.contains('dot')) {
                    const endDot = e.target;
                    const endRow = parseInt(endDot.getAttribute('data-row'));
                    const endCol = parseInt(endDot.getAttribute('data-col'));
                    const startRow = gameState.dragStart.row;
                    const startCol = gameState.dragStart.col;
                    
                    // Check if dots are adjacent (not diagonal)
                    const isAdjacent = (
                        (Math.abs(endRow - startRow) === 1 && endCol === startCol) || 
                        (Math.abs(endCol - startCol) === 1 && endRow === startRow)
                    );
                    
                    if (isAdjacent) {
                        // Determine if it's a horizontal or vertical line
                        if (endRow === startRow) {
                            // Horizontal line
                            const minCol = Math.min(startCol, endCol);
                            if (!gameState.horizontalLines[endRow][minCol]) {
                                gameState.horizontalLines[endRow][minCol] = true;
                                drawLine(startRow, startCol, endRow, endCol);
                                checkBoxCompletion(startRow, startCol, endRow, endCol);
                            }
                        } else {
                            // Vertical line
                            const minRow = Math.min(startRow, endRow);
                            if (!gameState.verticalLines[minRow][endCol]) {
                                gameState.verticalLines[minRow][endCol] = true;
                                drawLine(startRow, startCol, endRow, endCol);
                                checkBoxCompletion(startRow, startCol, endRow, endCol);
                            }
                        }
                    }
                }
                
                // Clean up
                removePreviewLine();
                gameState.dragStart = null;
            }

            // Cancel drag operation
            function cancelDrag() {
                removePreviewLine();
                gameState.dragStart = null;
            }

            // Create preview line
            function createPreviewLine(row, col, clientX, clientY) {
                const previewLayer = document.getElementById('preview-layer');
                const svg = document.querySelector('#game-board svg');
                const svgRect = svg.getBoundingClientRect();
                
                const startX = col * config.boxSize;
                const startY = row * config.boxSize;
                const endX = clientX - svgRect.left;
                const endY = clientY - svgRect.top;
                
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', startX);
                line.setAttribute('y1', startY);
                line.setAttribute('x2', endX);
                line.setAttribute('y2', endY);
                line.setAttribute('stroke', config.colors.previewLine);
                line.setAttribute('stroke-width', config.lineThickness);
                line.setAttribute('stroke-linecap', 'round');
                line.id = 'preview-line';
                
                previewLayer.appendChild(line);
                gameState.previewLine = line;
            }

            // Update preview line
            function updatePreviewLine(x, y) {
                if (!gameState.previewLine) return;
                
                gameState.previewLine.setAttribute('x2', x);
                gameState.previewLine.setAttribute('y2', y);
            }

            // Remove preview line
            function removePreviewLine() {
                if (gameState.previewLine) {
                    gameState.previewLine.remove();
                    gameState.previewLine = null;
                }
            }

            // Draw a permanent line
            function drawLine(startRow, startCol, endRow, endCol) {
                const linesLayer = document.getElementById('lines-layer');
                
                const startX = startCol * config.boxSize;
                const startY = startRow * config.boxSize;
                const endX = endCol * config.boxSize;
                const endY = endRow * config.boxSize;
                
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', startX);
                line.setAttribute('y1', startY);
                line.setAttribute('x2', endX);
                line.setAttribute('y2', endY);
                line.setAttribute('stroke', config.colors.line);
                line.setAttribute('stroke-width', config.lineThickness);
                line.setAttribute('stroke-linecap', 'round');
                
                linesLayer.appendChild(line);
            }

            // Check if a box is completed
            function checkBoxCompletion(startRow, startCol, endRow, endCol) {
                let boxCompleted = false;
                
                // Check if the line is horizontal or vertical
                if (startRow === endRow) {
                    // Horizontal line
                    const row = startRow;
                    const minCol = Math.min(startCol, endCol);
                    
                    // Check box above (if exists)
                    if (row > 0) {
                        const boxRow = row - 1;
                        const boxCol = minCol;
                        
                        if (isBoxCompleted(boxRow, boxCol)) {
                            fillBox(boxRow, boxCol, gameState.currentPlayer);
                            gameState.boxes[boxRow][boxCol] = gameState.currentPlayer;
                            gameState.scores[gameState.currentPlayer]++;
                            boxCompleted = true;
                        }
                    }
                    
                    // Check box below (if exists)
                    if (row < config.gridSize - 1) {
                        const boxRow = row;
                        const boxCol = minCol;
                        
                        if (isBoxCompleted(boxRow, boxCol)) {
                            fillBox(boxRow, boxCol, gameState.currentPlayer);
                            gameState.boxes[boxRow][boxCol] = gameState.currentPlayer;
                            gameState.scores[gameState.currentPlayer]++;
                            boxCompleted = true;
                        }
                    }
                } else {
                    // Vertical line
                    const col = startCol;
                    const minRow = Math.min(startRow, endRow);
                    
                    // Check box to the left (if exists)
                    if (col > 0) {
                        const boxRow = minRow;
                        const boxCol = col - 1;
                        
                        if (isBoxCompleted(boxRow, boxCol)) {
                            fillBox(boxRow, boxCol, gameState.currentPlayer);
                            gameState.boxes[boxRow][boxCol] = gameState.currentPlayer;
                            gameState.scores[gameState.currentPlayer]++;
                            boxCompleted = true;
                        }
                    }
                    
                    // Check box to the right (if exists)
                    if (col < config.gridSize - 1) {
                        const boxRow = minRow;
                        const boxCol = col;
                        
                        if (isBoxCompleted(boxRow, boxCol)) {
                            fillBox(boxRow, boxCol, gameState.currentPlayer);
                            gameState.boxes[boxRow][boxCol] = gameState.currentPlayer;
                            gameState.scores[gameState.currentPlayer]++;
                            boxCompleted = true;
                        }
                    }
                }
                
                // Update scores
                player1Score.textContent = gameState.scores[1];
                player2Score.textContent = gameState.scores[2];
                
                // Check if game is over
                const totalBoxes = (config.gridSize - 1) * (config.gridSize - 1);
                const filledBoxes = gameState.scores[1] + gameState.scores[2];
                
                if (filledBoxes === totalBoxes) {
                    // Game over
                    setTimeout(() => {
                        const winner = gameState.scores[1] > gameState.scores[2] ? 'Player 1' : 
                                    gameState.scores[1] < gameState.scores[2] ? 'Player 2' : 'It\'s a tie';
                        alert(`Game Over! ${winner} wins!`);
                    }, 100);
                } else if (!boxCompleted) {
                    // Switch player if no box was completed
                    switchPlayer();
                }
            }

            // Check if a box is completed
            function isBoxCompleted(row, col) {
                // Check if all four sides of the box are drawn
                const top = gameState.horizontalLines[row][col];
                const bottom = gameState.horizontalLines[row + 1][col];
                const left = gameState.verticalLines[row][col];
                const right = gameState.verticalLines[row][col + 1];
                
                return top && bottom && left && right;
            }

            // Fill a completed box
            function fillBox(row, col, player) {
                const boxesLayer = document.getElementById('boxes-layer');
                
                const x = col * config.boxSize;
                const y = row * config.boxSize;
                
                const box = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                box.setAttribute('x', x + config.lineThickness / 2);
                box.setAttribute('y', y + config.lineThickness / 2);
                box.setAttribute('width', config.boxSize - config.lineThickness);
                box.setAttribute('height', config.boxSize - config.lineThickness);
                box.setAttribute('fill', player === 1 ? config.colors.player1Box : config.colors.player2Box);
                box.setAttribute('rx', 4); // Rounded corners
                
                boxesLayer.appendChild(box);
            }

            // Switch player
            function switchPlayer() {
                gameState.currentPlayer = gameState.currentPlayer === 1 ? 2 : 1;
                gameState.turnCount++;
                
                // Update UI
                turnCount.textContent = gameState.turnCount;
                
                // Update player turn indicators
                if (gameState.currentPlayer === 1) {
                    player1Turn.querySelector('.player-arrow').classList.remove('opacity-0');
                    player2Turn.querySelector('.player-arrow').classList.add('opacity-0');
                } else {
                    player1Turn.querySelector('.player-arrow').classList.add('opacity-0');
                    player2Turn.querySelector('.player-arrow').classList.remove('opacity-0');
                }
            }

            // Reset the game
            function resetGame() {
                // Reset game state
                gameState.currentPlayer = 1;
                gameState.scores = { 1: 0, 2: 0 };
                gameState.turnCount = 1;
                gameState.horizontalLines = Array(config.gridSize).fill().map(() => Array(config.gridSize - 1).fill(false));
                gameState.verticalLines = Array(config.gridSize - 1).fill().map(() => Array(config.gridSize).fill(false));
                gameState.boxes = Array(config.gridSize - 1).fill().map(() => Array(config.gridSize - 1).fill(0));
                
                // Reset UI
                player1Score.textContent = '0';
                player2Score.textContent = '0';
                turnCount.textContent = '1';
                player1Turn.querySelector('.player-arrow').classList.remove('opacity-0');
                player2Turn.querySelector('.player-arrow').classList.add('opacity-0');
                
                // Reinitialize the board
                initializeBoard();
            }

            // Add event listener for reset button
            resetButton.addEventListener('click', resetGame);

            // Initialize the game
            initializeBoard();
        });
    </script>
</body>
</html>